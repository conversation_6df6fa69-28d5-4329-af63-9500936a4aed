@import url("../Generated/GeneratedIcons-MenuBar.uss");

/* MenuBar 组件样式 */

/* 菜单栏根容器 */
.menu-bar {
    flex-direction: row;
    align-items: center;
    height: 100%;
    padding: 0 8px;
}

/* 菜单分类容器 */
.menu-categories {
    flex-direction: row;
    align-items: center;
    height: 100%;
}

/* 菜单按钮 */
.menu-button {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210);
    padding: 6px 12px;
    margin: 0 2px;
    -unity-text-align: middle-center;
    font-size: 12px;
    border-radius: 3px;
    min-width: 50px;
    height: 28px;
    /* 添加过渡效果 */
    transition-duration: 0.1s;
    transition-property: background-color, color;
}

.menu-button:hover {
    background-color: rgba(96, 96, 96, 0.8);
}

.menu-button:active {
    background-color: rgba(48, 48, 48, 0.8);
}

/* 菜单按钮激活状态（下拉菜单打开时） */
.menu-button.active {
    background-color: rgba(62, 95, 150, 0.8);
    color: rgb(255, 255, 255);
}

/* 菜单按钮禁用状态 */
.menu-button:disabled {
    opacity: 0.5;
    color: rgb(128, 128, 128);
}

/* 下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(35, 35, 35);
    border-radius: 2px;
    padding: 4px 0;
    margin-top: 2px;
    width: 180px;
    min-width: 160px;
    display: none;
    /* 添加阴影效果 */
}

.dropdown-menu.visible {
    display: flex;
    flex-direction: column;
}

/* 下拉菜单项 */
.dropdown-item {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210);
    padding: 7px 16px;
    margin: 0 2px;
    -unity-text-align: middle-left;
    font-size: 12px;
    border-radius: 2px;
    height: 22px;
    min-height: 22px;
    /* 添加过渡效果 */
    transition-duration: 0.1s;
    transition-property: background-color, color;
}

.dropdown-item:hover {
    background-color: rgb(62, 95, 150);
    color: rgb(255, 255, 255);
}

.dropdown-item:active {
    background-color: rgb(51, 80, 128);
    color: rgb(255, 255, 255);
}

/* 下拉菜单项禁用状态 */
.dropdown-item:disabled {
    opacity: 0.5;
    color: rgb(128, 128, 128);
}

.dropdown-item:disabled:hover {
    background-color: transparent;
    color: rgb(128, 128, 128);
}

/* 下拉菜单分隔符 */
.dropdown-separator {
    height: 1px;
    background-color: rgb(35, 35, 35);
    margin: 4px 8px;
}

/* 菜单项图标样式现在由GeneratedIcons-MenuBar.uss提供 */

/* 菜单项快捷键 */
.menu-item-shortcut {
    color: rgb(160, 160, 160);
    font-size: 11px;
    margin-left: auto;
    padding-left: 16px;
}

/* 菜单项容器（包含图标、文本、快捷键） */
.menu-item-container {
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 100%;
}

/* 子菜单指示器 */
.submenu-indicator {
    width: 8px;
    height: 8px;
    margin-left: auto;
    background-color: rgb(210, 210, 210);
    /* 可以用CSS绘制箭头或使用背景图片 */
}

/* 子菜单容器 */
.submenu {
    position: absolute;
    left: 100%;
    top: -4px;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(35, 35, 35);
    border-radius: 2px;
    padding: 4px 0;
    width: 180px;
    min-width: 160px;
    display: none;
}

.submenu.visible {
    display: flex;
    flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 800px) {
    .menu-button {
        min-width: 40px;
        padding: 6px 8px;
    }
    
    .dropdown-menu {
        width: 160px;
        min-width: 140px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .menu-button {
        border-width: 1px;
        border-color: rgb(128, 128, 128);
    }
    
    .dropdown-menu {
        border-width: 2px;
    }
    
    .dropdown-item:hover {
        background-color: rgb(0, 120, 215);
    }
}
