/* 左侧面板根容器 */
.left-panel {
    width: 100%;
    min-width: 200px;
    background-color: rgba(48, 48, 48, 0.95);
    border-right-width: 1px;
    border-right-color: rgb(32, 32, 32);
    flex-direction: column;
    position: relative;
}

/* 面板标题栏 */
.panel-header {
    flex-direction: row;
    background-color: rgba(64, 64, 64, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgb(32, 32, 32);
    padding: 6px 8px;
    align-items: center;
    justify-content: space-between;
    min-height: 28px;
}

.panel-title {
    color: rgb(210, 210, 210);
    font-size: 12px;
    -unity-font-style: bold;
    flex-grow: 1;
}

.header-controls {
    flex-direction: row;
    align-items: center;
}

.header-button {
    background-color: transparent;
    border-width: 0;
    width: 20px;
    height: 20px;
    margin: 0 2px;
    padding: 2px;
    border-radius: 2px;
    align-items: center;
    justify-content: center;
}

.header-button:hover {
    background-color: rgba(96, 96, 96, 0.6);
}

.header-button:active {
    background-color: rgba(48, 48, 48, 0.8);
}

/* 图标样式 */
.collapse-icon,
.refresh-icon,
.settings-icon,
.expand-icon,
.collapse-all-icon,
.clear-icon,
.empty-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
}

/* 面板工具栏 */
.panel-toolbar {
    flex-direction: row;
    background-color: rgba(56, 56, 56, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgb(32, 32, 32);
    padding: 4px 8px;
    align-items: center;
    min-height: 24px;
}

.search-field {
    flex-grow: 1;
    margin-right: 4px;
}

.search-field > .unity-base-text-field__input {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
}

.search-field > .unity-base-text-field__input:focus {
    border-color: rgb(58, 121, 187);
}

.search-clear-button {
    background-color: transparent;
    border-width: 0;
    width: 16px;
    height: 16px;
    padding: 2px;
    margin-right: 4px;
}

.search-clear-button:hover {
    background-color: rgba(96, 96, 96, 0.6);
}

.toolbar-separator {
    width: 1px;
    height: 16px;
    background-color: rgb(96, 96, 96);
    margin: 0 4px;
}

.toolbar-button {
    background-color: transparent;
    border-width: 0;
    width: 20px;
    height: 20px;
    margin: 0 1px;
    padding: 2px;
    border-radius: 2px;
}

.toolbar-button:hover {
    background-color: rgba(96, 96, 96, 0.6);
}

/* 主内容区域 */
.panel-content {
    flex-grow: 1;
    position: relative;
}

.hierarchy-scroll {
    flex-grow: 1;
}

.hierarchy-tree {
    flex-grow: 1;
    background-color: transparent;
    padding: 4px;
}

/* 树视图项样式 */
.unity-tree-view__item {
    padding: 2px 4px;
    margin: 1px 0;
    border-radius: 2px;
    color: rgb(210, 210, 210);
    font-size: 11px;
}

.unity-tree-view__item:hover {
    background-color: rgba(96, 96, 96, 0.4);
}

.unity-tree-view__item:selected {
    background-color: rgba(58, 121, 187, 0.6);
}

.unity-tree-view__item-toggle {
    width: 12px;
    height: 12px;
    margin-right: 4px;
}

/* 空状态 */
.empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    display: none;
}

.empty-icon {
    width: 48px;
    height: 48px;
    background-color: rgb(128, 128, 128);
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-message {
    color: rgb(160, 160, 160);
    font-size: 14px;
    -unity-font-style: bold;
    margin-bottom: 4px;
}

.empty-hint {
    color: rgb(128, 128, 128);
    font-size: 11px;
    -unity-text-align: middle-center;

}

/* 底部状态栏 */
.panel-status {
    flex-direction: row;
    background-color: rgba(56, 56, 56, 0.9);
    border-top-width: 1px;
    border-top-color: rgb(32, 32, 32);
    padding: 4px 8px;
    align-items: center;
    min-height: 20px;
}

.status-text {
    color: rgb(160, 160, 160);
    font-size: 10px;
}

.status-separator {
    width: 1px;
    height: 12px;
    background-color: rgb(96, 96, 96);
    margin: 0 8px;
}

/* 调整大小手柄 */
.resize-handle {
    position: absolute;
    right: -2px;  /* 稍微扩大可点击区域 */
    top: 0;
    bottom: 0;
    width: 6px;   /* 增加宽度以改善用户体验 */
    background-color: transparent;
    cursor: resize-horizontal;
}

.resize-handle:hover {
    background-color: rgba(58, 121, 187, 0.4);
    border-right-width: 2px;
    border-right-color: rgba(58, 121, 187, 0.8);
}

.resize-handle:active {
    background-color: rgba(58, 121, 187, 0.6);
}

/* 上下文菜单 */
.context-menu {
    position: absolute;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(32, 32, 32);
    border-radius: 4px;
    padding: 4px 0;
    min-width: 120px;
    display: none;
}

.context-menu-item {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210);
    padding: 4px 12px;
    margin: 0;
    -unity-text-align: middle-left;
    font-size: 11px;
    border-radius: 0;
    width: 100%;
}

.context-menu-item:hover {
    background-color: rgba(96, 96, 96, 0.8);
}

.context-menu-item:active {
    background-color: rgba(58, 121, 187, 0.6);
}

.context-menu-item.danger {
    color: rgb(244, 67, 54);
}

.context-menu-item.danger:hover {
    background-color: rgba(244, 67, 54, 0.2);
}

.context-menu-separator {
    height: 1px;
    background-color: rgb(96, 96, 96);
    margin: 2px 8px;
}

/* 折叠状态 */
.left-panel.collapsed {
    width: 32px;
    min-width: 32px;
}

.left-panel.collapsed .panel-title,
.left-panel.collapsed .panel-toolbar,
.left-panel.collapsed .panel-content,
.left-panel.collapsed .panel-status {
    display: none;
}

.left-panel.collapsed .panel-header {
    justify-content: center;
    padding: 6px 4px;
}

.left-panel.collapsed .header-controls {
    flex-direction: column;
}

/* 拖拽状态 */
.left-panel.resizing {
    opacity: 0.95;
}

.left-panel.resizing .resize-handle {
    background-color: rgba(58, 121, 187, 0.6);
    border-right-width: 2px;
    border-right-color: rgba(58, 121, 187, 1.0);
}
